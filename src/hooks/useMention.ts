/**
 * Custom hook for managing mention functionality in chat input
 */

import { useState, useCallback, useRef, useEffect } from 'react';
import {
  MentionInputState,
  MentionPopoverState,
  Mention,
  MentionUser,
  MentionEvent,
  MentionConfig,
  DEFAULT_MENTION_CONFIG,
  UseMentionReturn,
  MentionSearchOptions,
} from '@/types/mention';
import { fetchMentionUsers, debouncedMentionSearch, cancelDebouncedSearch } from '@/services/mentionService';

/**
 * Hook for managing mention functionality
 */
export function useMention(
  config: Partial<MentionConfig> = {},
  searchOptions: Partial<MentionSearchOptions> = {}
): UseMentionReturn {
  const mergedConfig = { ...DEFAULT_MENTION_CONFIG, ...config };
  
  // State management
  const [state, setState] = useState<MentionInputState>({
    mentions: [],
    popover: {
      isOpen: false,
      position: { top: 0, left: 0 },
      triggerIndex: -1,
      searchQuery: '',
      selectedIndex: 0,
      suggestions: [],
      loading: false,
    },
    lastCursorPosition: 0,
  });

  // Refs for managing state
  const mentionIdCounter = useRef(0);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Generate unique mention ID
  const generateMentionId = useCallback((): string => {
    return `mention-${Date.now()}-${++mentionIdCounter.current}`;
  }, []);

  // Open popover
  const openPopover = useCallback((position: { top: number; left: number }, triggerIndex: number) => {
    setState(prev => ({
      ...prev,
      popover: {
        ...prev.popover,
        isOpen: true,
        position,
        triggerIndex,
        searchQuery: '',
        selectedIndex: 0,
        suggestions: [],
        loading: true,
      },
    }));

    // Start searching for users
    searchUsers('');
  }, []);

  // Close popover
  const closePopover = useCallback(() => {
    setState(prev => ({
      ...prev,
      popover: {
        ...prev.popover,
        isOpen: false,
        searchQuery: '',
        selectedIndex: 0,
        suggestions: [],
        loading: false,
      },
    }));

    // Cancel any pending search
    cancelDebouncedSearch();
  }, []);

  // Update search query
  const updateSearchQuery = useCallback((query: string) => {
    setState(prev => ({
      ...prev,
      popover: {
        ...prev.popover,
        searchQuery: query,
        selectedIndex: 0,
        loading: true,
      },
    }));

    // Debounced search
    searchUsers(query);
  }, []);

  // Search users function
  const searchUsers = useCallback(async (query: string) => {
    try {
      const options = {
        query,
        limit: mergedConfig.maxSuggestions,
        ...searchOptions,
      };

      debouncedMentionSearch(options, (result) => {
        setState(prev => ({
          ...prev,
          popover: {
            ...prev.popover,
            suggestions: result.users,
            loading: false,
          },
        }));
      });
    } catch (error) {
      console.error('Error searching users:', error);
      setState(prev => ({
        ...prev,
        popover: {
          ...prev.popover,
          suggestions: [],
          loading: false,
        },
      }));
    }
  }, [mergedConfig.maxSuggestions, searchOptions]);

  // Select suggestion by index
  const selectSuggestion = useCallback((index: number) => {
    setState(prev => ({
      ...prev,
      popover: {
        ...prev.popover,
        selectedIndex: Math.max(0, Math.min(index, prev.popover.suggestions.length - 1)),
      },
    }));
  }, []);

  // Add mention
  const addMention = useCallback((user: MentionUser, position: { start: number; end: number }) => {
    const mention: Mention = {
      id: generateMentionId(),
      userId: user.id,
      displayName: `${user.firstName} ${user.lastName}`,
      startIndex: position.start,
      endIndex: position.end,
      trigger: mergedConfig.trigger,
    };

    setState(prev => ({
      ...prev,
      mentions: [...prev.mentions, mention],
    }));

    closePopover();
    return mention;
  }, [generateMentionId, mergedConfig.trigger, closePopover]);

  // Remove mention
  const removeMention = useCallback((mentionId: string) => {
    setState(prev => ({
      ...prev,
      mentions: prev.mentions.filter(m => m.id !== mentionId),
    }));
  }, []);

  // Update mentions (for external updates)
  const updateMentions = useCallback((mentions: Mention[]) => {
    setState(prev => ({
      ...prev,
      mentions,
    }));
  }, []);

  // Get mention at specific position
  const getMentionAtPosition = useCallback((position: number): Mention | null => {
    return state.mentions.find(
      mention => position >= mention.startIndex && position <= mention.endIndex
    ) || null;
  }, [state.mentions]);

  // Get mention by ID
  const getMentionById = useCallback((id: string): Mention | null => {
    return state.mentions.find(mention => mention.id === id) || null;
  }, [state.mentions]);

  // Format mention display
  const formatMentionDisplay = useCallback((mention: Mention, user?: MentionUser): string => {
    const displayName = user ? `${user.firstName} ${user.lastName}` : mention.displayName;
    return `${mergedConfig.trigger}${displayName}`;
  }, [mergedConfig.trigger]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      cancelDebouncedSearch();
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  return {
    state,
    actions: {
      openPopover,
      closePopover,
      updateSearchQuery,
      selectSuggestion,
      addMention,
      removeMention,
      updateMentions,
      searchUsers,
    },
    utils: {
      getMentionAtPosition,
      getMentionById,
      formatMentionDisplay,
    },
  };
}

/**
 * Utility functions for mention parsing and processing
 */

// Parse text content to extract mentions
export function parseMentionsFromText(text: string, trigger: string = '@'): Mention[] {
  const mentions: Mention[] = [];
  const regex = new RegExp(`\\${trigger}([\\w\\s]+)`, 'g');
  let match;

  while ((match = regex.exec(text)) !== null) {
    mentions.push({
      id: `parsed-${Date.now()}-${mentions.length}`,
      userId: 0, // Will need to be resolved later
      displayName: match[1].trim(),
      startIndex: match.index,
      endIndex: match.index + match[0].length,
      trigger,
    });
  }

  return mentions;
}

// Replace mentions in text with formatted versions
export function replaceMentionsInText(
  text: string,
  mentions: Mention[],
  users: MentionUser[],
  formatter: (mention: Mention, user?: MentionUser) => string
): string {
  let result = text;
  
  // Sort mentions by start index in descending order to avoid index shifting
  const sortedMentions = [...mentions].sort((a, b) => b.startIndex - a.startIndex);
  
  sortedMentions.forEach(mention => {
    const user = users.find(u => u.id === mention.userId);
    const replacement = formatter(mention, user);
    result = result.slice(0, mention.startIndex) + replacement + result.slice(mention.endIndex);
  });
  
  return result;
}

// Get cursor position in contentEditable element
export function getCursorPosition(element: HTMLElement): number {
  const selection = window.getSelection();
  if (!selection || selection.rangeCount === 0) return 0;
  
  const range = selection.getRangeAt(0);
  const preCaretRange = range.cloneRange();
  preCaretRange.selectNodeContents(element);
  preCaretRange.setEnd(range.endContainer, range.endOffset);
  
  return preCaretRange.toString().length;
}

// Set cursor position in contentEditable element
export function setCursorPosition(element: HTMLElement, position: number): void {
  const selection = window.getSelection();
  if (!selection) return;
  
  const range = document.createRange();
  let currentPos = 0;
  let walker = document.createTreeWalker(
    element,
    NodeFilter.SHOW_TEXT,
    null
  );
  
  let node;
  while ((node = walker.nextNode())) {
    const textLength = node.textContent?.length || 0;
    if (currentPos + textLength >= position) {
      range.setStart(node, position - currentPos);
      range.setEnd(node, position - currentPos);
      break;
    }
    currentPos += textLength;
  }
  
  selection.removeAllRanges();
  selection.addRange(range);
}

// Get caret coordinates relative to viewport
export function getCaretCoordinates(element: HTMLElement): { top: number; left: number } {
  const selection = window.getSelection();
  if (!selection || selection.rangeCount === 0) {
    return { top: 0, left: 0 };
  }
  
  const range = selection.getRangeAt(0);
  const rect = range.getBoundingClientRect();
  
  return {
    top: rect.bottom + window.scrollY,
    left: rect.left + window.scrollX,
  };
}
