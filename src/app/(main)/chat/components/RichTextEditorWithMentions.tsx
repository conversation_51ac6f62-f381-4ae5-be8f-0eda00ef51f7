'use client';

import React, { useRef, useCallback, useEffect, useState } from 'react';
import styled from 'styled-components';
import { Bold, List, AtSign } from 'lucide-react';
import { appTheme } from '@/app/theme';
import { useMention, getCursorPosition, getCaretCoordinates } from '@/hooks/useMention';
import MentionPopover from './MentionPopover';
import { InputMention } from './MentionDisplay';
import { Mention, MentionUser, MentionSearchOptions } from '@/types/mention';

interface RichTextEditorWithMentionsProps {
  placeholder?: string;
  value?: string;
  onChange?: (htmlContent: string, textContent: string, mentions: Mention[]) => void;
  onKeyDown?: (e: React.KeyboardEvent) => void;
  disabled?: boolean;
  maxHeight?: string;
  minHeight?: string;
  // Mention-specific props
  mentionSearchOptions?: Partial<MentionSearchOptions>;
  onMentionClick?: (mention: Mention, user?: MentionUser) => void;
}

const EditorContainer = styled.div`
  display: flex;
  flex-direction: column;
  flex: 1;
  border: none;
  background: transparent;
  position: relative;
`;

const ToolbarContainer = styled.div`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.xs};
  border-bottom: 1px solid ${appTheme.colors.border};
  margin-bottom: ${appTheme.spacing.xs};
`;

const ToolbarButton = styled.button<{ $active?: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 25px;
  border: none;
  border-radius: ${appTheme.borderRadius.sm};
  background: transparent;
  color: ${props => props.$active ? appTheme.colors.primary : appTheme.colors.text.secondary};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: ${props => props.$active ? appTheme.colors.primaryLight : appTheme.colors.background.lighter};
    color: ${props => props.$active ? appTheme.colors.primary : appTheme.colors.text.primary};
  }

  &:active {
    transform: scale(0.95);
  }

  /* Mobile - touch-friendly sizing */
  @media (max-width: ${appTheme.breakpoints.md}) {
    width: 32px;
    height: 32px;
  }
`;

const EditorContent = styled.div<{ $maxHeight?: string; $minHeight?: string }>`
  flex: 1;
  outline: none;
  font-size: 14px;
  font-family: inherit;
  line-height: 1.4;
  color: ${appTheme.colors.text.primary};
  max-height: ${props => props.$maxHeight || '120px'};
  min-height: ${props => props.$minHeight || '20px'};
  overflow-y: auto;
  word-wrap: break-word;
  overflow-wrap: break-word;

  &:empty::before {
    content: attr(data-placeholder);
    color: ${appTheme.colors.text.light};
    pointer-events: none;
  }

  /* Style for bold text */
  strong, b {
    font-weight: 600;
  }

  /* Style for bullet lists */
  ul {
    margin: 0;
    padding-left: 20px;
    list-style-type: disc;
  }

  li {
    margin: 2px 0;
  }

  /* Prevent nested lists for simplicity */
  ul ul {
    display: none;
  }

  /* Mobile - larger font and better touch experience */
  @media (max-width: ${appTheme.breakpoints.md}) {
    font-size: 16px; /* Prevents zoom on iOS */
    line-height: 1.5;
    min-height: 24px;
    max-height: 100px;
  }

  /* Small mobile - optimize for smaller screens */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    max-height: 80px;
  }

  /* Enhanced scrollbar styling */
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: ${appTheme.colors.border};
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: ${appTheme.colors.text.light};
  }
`;

export default function RichTextEditorWithMentions({
  placeholder = 'Type your message...',
  value = '',
  onChange,
  onKeyDown,
  disabled = false,
  maxHeight,
  minHeight,
  mentionSearchOptions = {},
  onMentionClick,
}: RichTextEditorWithMentionsProps) {
  const editorRef = useRef<HTMLDivElement>(null);
  const [isBoldActive, setIsBoldActive] = useState(false);
  const [isListActive, setIsListActive] = useState(false);
  const [lastTextContent, setLastTextContent] = useState('');

  // Initialize mention functionality
  const mention = useMention({}, mentionSearchOptions);

  // Update editor content when value prop changes
  useEffect(() => {
    if (editorRef.current && editorRef.current.innerHTML !== value) {
      editorRef.current.innerHTML = value;
      setLastTextContent(editorRef.current.textContent || '');
    }
  }, [value]);

  // Check current formatting state
  const updateFormattingState = useCallback(() => {
    if (!editorRef.current) return;

    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;

    // Check if bold is active
    const boldActive = document.queryCommandState('bold');
    setIsBoldActive(boldActive);

    // Check if we're in a list
    const range = selection.getRangeAt(0);
    let node: Node | null = range.commonAncestorContainer;
    if (node.nodeType === Node.TEXT_NODE) {
      node = node.parentNode;
    }
    
    let listActive = false;
    let currentNode = node as Element;
    while (currentNode && currentNode !== editorRef.current) {
      if (currentNode.tagName === 'UL' || currentNode.tagName === 'LI') {
        listActive = true;
        break;
      }
      currentNode = currentNode.parentElement as Element;
    }
    setIsListActive(listActive);
  }, []);

  // Handle mention trigger detection
  const checkForMentionTrigger = useCallback(() => {
    if (!editorRef.current) return;

    const textContent = editorRef.current.textContent || '';
    const cursorPos = getCursorPosition(editorRef.current);
    
    // Look for '@' character before cursor
    const beforeCursor = textContent.slice(0, cursorPos);
    const atIndex = beforeCursor.lastIndexOf('@');
    
    if (atIndex !== -1) {
      // Check if '@' is at start or preceded by whitespace
      const charBeforeAt = atIndex > 0 ? beforeCursor[atIndex - 1] : ' ';
      if (charBeforeAt === ' ' || charBeforeAt === '\n' || atIndex === 0) {
        const searchQuery = beforeCursor.slice(atIndex + 1);
        
        // Only trigger if search query doesn't contain spaces (unless configured to allow)
        if (!searchQuery.includes(' ') || searchQuery.length === 0) {
          const coordinates = getCaretCoordinates(editorRef.current);
          mention.actions.openPopover(coordinates, atIndex);
          mention.actions.updateSearchQuery(searchQuery);
          return;
        }
      }
    }
    
    // Close popover if no valid trigger found
    if (mention.state.popover.isOpen) {
      mention.actions.closePopover();
    }
  }, [mention]);

  // Handle content changes
  const handleInput = useCallback(() => {
    if (!editorRef.current || !onChange) return;

    const htmlContent = editorRef.current.innerHTML;
    const textContent = editorRef.current.textContent || '';
    
    // Check for mention triggers
    checkForMentionTrigger();
    
    // Update search query if popover is open
    if (mention.state.popover.isOpen) {
      const cursorPos = getCursorPosition(editorRef.current);
      const beforeCursor = textContent.slice(0, cursorPos);
      const searchStart = mention.state.popover.triggerIndex + 1;
      const searchQuery = beforeCursor.slice(searchStart);
      
      if (searchQuery !== mention.state.popover.searchQuery) {
        mention.actions.updateSearchQuery(searchQuery);
      }
    }
    
    onChange(htmlContent, textContent, mention.state.mentions);
    updateFormattingState();
    setLastTextContent(textContent);
  }, [onChange, updateFormattingState, checkForMentionTrigger, mention]);

  // Handle user selection from mention popover
  const handleMentionSelect = useCallback((user: MentionUser) => {
    if (!editorRef.current) return;

    const textContent = editorRef.current.textContent || '';
    const triggerIndex = mention.state.popover.triggerIndex;
    const cursorPos = getCursorPosition(editorRef.current);
    
    // Calculate mention text to replace
    const beforeMention = textContent.slice(0, triggerIndex);
    const afterMention = textContent.slice(cursorPos);
    const mentionText = `@${user.firstName} ${user.lastName}`;
    
    // Create new content with mention
    const newTextContent = beforeMention + mentionText + afterMention;
    
    // Add mention to state
    const newMention = mention.actions.addMention(user, {
      start: triggerIndex,
      end: triggerIndex + mentionText.length,
    });

    // Update editor content
    const mentionElement = document.createElement('span');
    mentionElement.setAttribute('data-mention-id', newMention.id);
    mentionElement.setAttribute('contenteditable', 'false');
    mentionElement.style.display = 'inline-block';
    
    // Create React element and render it
    const mentionComponent = React.createElement(InputMention, {
      mention: newMention,
      user: user,
      onClick: onMentionClick,
      onRemove: () => mention.actions.removeMention(newMention.id),
    });

    // For now, insert plain text (in a real implementation, you'd render the React component)
    const selection = window.getSelection();
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      range.setStart(editorRef.current.childNodes[0] || editorRef.current, triggerIndex);
      range.setEnd(editorRef.current.childNodes[0] || editorRef.current, cursorPos);
      range.deleteContents();
      range.insertNode(document.createTextNode(mentionText + ' '));
      
      // Move cursor after mention
      range.setStartAfter(range.endContainer);
      range.collapse(true);
      selection.removeAllRanges();
      selection.addRange(range);
    }

    handleInput();
  }, [mention, onMentionClick, handleInput]);

  // Handle selection changes
  const handleSelectionChange = useCallback(() => {
    updateFormattingState();
  }, [updateFormattingState]);

  // Handle key events
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    // Handle mention popover navigation
    if (mention.state.popover.isOpen) {
      if (e.key === 'ArrowDown') {
        e.preventDefault();
        mention.actions.selectSuggestion(mention.state.popover.selectedIndex + 1);
        return;
      }
      if (e.key === 'ArrowUp') {
        e.preventDefault();
        mention.actions.selectSuggestion(mention.state.popover.selectedIndex - 1);
        return;
      }
      if (e.key === 'Enter') {
        e.preventDefault();
        const selectedUser = mention.state.popover.suggestions[mention.state.popover.selectedIndex];
        if (selectedUser) {
          handleMentionSelect(selectedUser);
        }
        return;
      }
      if (e.key === 'Escape') {
        e.preventDefault();
        mention.actions.closePopover();
        return;
      }
    }

    // Handle Enter key for list items
    if (e.key === 'Enter' && isListActive && !e.shiftKey) {
      e.preventDefault();
      e.stopPropagation();

      const selection = window.getSelection();
      if (!selection || selection.rangeCount === 0) return;

      const range = selection.getRangeAt(0);
      let listItem: Node | null = range.commonAncestorContainer;

      // Find the current list item
      while (listItem && (listItem.nodeType !== Node.ELEMENT_NODE || (listItem as Element).tagName !== 'LI')) {
        listItem = listItem.parentNode;
      }

      if (listItem && (listItem as Element).tagName === 'LI') {
        const currentLi = listItem as HTMLLIElement;
        
        // If the current list item is empty, exit the list
        if (!currentLi.textContent?.trim()) {
          const ul = currentLi.parentElement;
          if (ul && ul.tagName === 'UL') {
            currentLi.remove();
            
            const newP = document.createElement('div');
            newP.innerHTML = '<br>';
            ul.parentNode?.insertBefore(newP, ul.nextSibling);
            
            const newRange = document.createRange();
            newRange.setStart(newP, 0);
            newRange.collapse(true);
            selection.removeAllRanges();
            selection.addRange(newRange);
          }
        } else {
          const newLi = document.createElement('li');
          newLi.innerHTML = '<br>';
          currentLi.parentNode?.insertBefore(newLi, currentLi.nextSibling);
          
          const newRange = document.createRange();
          newRange.setStart(newLi, 0);
          newRange.collapse(true);
          selection.removeAllRanges();
          selection.addRange(newRange);
        }
        
        handleInput();
        return;
      }
    }

    if (onKeyDown) {
      onKeyDown(e);
    }
  }, [onKeyDown, isListActive, handleInput, mention, handleMentionSelect]);

  // Format text as bold
  const toggleBold = useCallback(() => {
    if (!editorRef.current) return;
    
    editorRef.current.focus();
    document.execCommand('bold', false);
    updateFormattingState();
    handleInput();
  }, [updateFormattingState, handleInput]);

  // Toggle bullet list
  const toggleList = useCallback(() => {
    if (!editorRef.current) return;
    
    editorRef.current.focus();
    
    if (isListActive) {
      document.execCommand('insertHTML', false, '<div><br></div>');
    } else {
      document.execCommand('insertUnorderedList', false);
    }
    
    updateFormattingState();
    handleInput();
  }, [isListActive, updateFormattingState, handleInput]);

  // Set up event listeners
  useEffect(() => {
    document.addEventListener('selectionchange', handleSelectionChange);
    return () => {
      document.removeEventListener('selectionchange', handleSelectionChange);
    };
  }, [handleSelectionChange]);

  return (
    <EditorContainer>
      <ToolbarContainer>
        <ToolbarButton
          type="button"
          $active={isBoldActive}
          onClick={toggleBold}
          title="Bold (Ctrl+B)"
          disabled={disabled}
        >
          <Bold size={14} />
        </ToolbarButton>
        <ToolbarButton
          type="button"
          $active={isListActive}
          onClick={toggleList}
          title="Bullet List"
          disabled={disabled}
        >
          <List size={14} />
        </ToolbarButton>
        <ToolbarButton
          type="button"
          onClick={() => {
            if (editorRef.current) {
              editorRef.current.focus();
              document.execCommand('insertText', false, '@');
              handleInput();
            }
          }}
          title="Mention someone (@)"
          disabled={disabled}
        >
          <AtSign size={14} />
        </ToolbarButton>
      </ToolbarContainer>
      
      <EditorContent
        ref={editorRef}
        contentEditable={!disabled}
        data-placeholder={placeholder}
        onInput={handleInput}
        onKeyDown={handleKeyDown}
        $maxHeight={maxHeight}
        $minHeight={minHeight}
        suppressContentEditableWarning={true}
      />

      <MentionPopover
        isOpen={mention.state.popover.isOpen}
        position={mention.state.popover.position}
        suggestions={mention.state.popover.suggestions}
        selectedIndex={mention.state.popover.selectedIndex}
        loading={mention.state.popover.loading}
        onSelect={handleMentionSelect}
        onClose={mention.actions.closePopover}
        searchQuery={mention.state.popover.searchQuery}
      />
    </EditorContainer>
  );
}
