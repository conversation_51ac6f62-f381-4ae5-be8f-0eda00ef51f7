'use client';

import React, { useEffect, useRef } from 'react';
import styled from 'styled-components';
import { User, Loader2 } from 'lucide-react';
import { appTheme } from '@/app/theme';
import { MentionPopoverProps, MentionSuggestion, MentionUser } from '@/types/mention';

const PopoverContainer = styled.div<{ $position: { top: number; left: number } }>`
  position: fixed;
  top: ${props => props.$position.top}px;
  left: ${props => props.$position.left}px;
  z-index: 1000;
  background: ${appTheme.colors.background.main};
  border: 1px solid ${appTheme.colors.border};
  border-radius: ${appTheme.borderRadius.lg};
  box-shadow: ${appTheme.shadows.lg};
  min-width: 280px;
  max-width: 320px;
  max-height: 300px;
  overflow: hidden;
  backdrop-filter: blur(10px);
  animation: slideIn 0.2s cubic-bezier(0.4, 0, 0.2, 1);

  @keyframes slideIn {
    0% {
      opacity: 0;
      transform: translateY(-8px) scale(0.95);
    }
    100% {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  /* Mobile adjustments */
  @media (max-width: ${appTheme.breakpoints.md}) {
    min-width: 260px;
    max-width: 300px;
    max-height: 250px;
    border-radius: ${appTheme.borderRadius.md};
  }

  /* Small mobile adjustments */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    min-width: 240px;
    max-width: 280px;
    max-height: 200px;
  }
`;

const PopoverHeader = styled.div`
  padding: ${appTheme.spacing.sm} ${appTheme.spacing.md};
  border-bottom: 1px solid ${appTheme.colors.border};
  background: ${appTheme.colors.background.lighter};
  font-size: 12px;
  font-weight: 600;
  color: ${appTheme.colors.text.secondary};
  text-transform: uppercase;
  letter-spacing: 0.5px;

  /* Mobile adjustments */
  @media (max-width: ${appTheme.breakpoints.md}) {
    padding: ${appTheme.spacing.xs} ${appTheme.spacing.sm};
    font-size: 11px;
  }
`;

const SuggestionsList = styled.div`
  max-height: 240px;
  overflow-y: auto;
  padding: ${appTheme.spacing.xs} 0;

  /* Custom scrollbar */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: ${appTheme.colors.border};
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: ${appTheme.colors.text.light};
  }

  /* Mobile adjustments */
  @media (max-width: ${appTheme.breakpoints.md}) {
    max-height: 180px;
    padding: ${appTheme.spacing.xs} 0;

    &::-webkit-scrollbar {
      width: 4px;
    }
  }
`;

const SuggestionItem = styled.div<{ $selected: boolean }>`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.sm};
  padding: ${appTheme.spacing.sm} ${appTheme.spacing.md};
  cursor: pointer;
  transition: all 0.2s ease;
  background: ${props => props.$selected ? appTheme.colors.primary : 'transparent'};
  color: ${props => props.$selected ? 'white' : appTheme.colors.text.primary};

  &:hover {
    background: ${props => props.$selected ? appTheme.colors.primaryHover : appTheme.colors.background.lighter};
  }

  &:active {
    transform: scale(0.98);
  }

  /* Mobile adjustments */
  @media (max-width: ${appTheme.breakpoints.md}) {
    padding: ${appTheme.spacing.md} ${appTheme.spacing.sm};
    gap: ${appTheme.spacing.md};
    min-height: 48px; /* Touch-friendly height */

    &:active {
      transform: scale(0.95);
      background: ${props => props.$selected ? appTheme.colors.primaryHover : appTheme.colors.background.lighter};
    }
  }
`;

const UserAvatar = styled.div<{ $imageUrl?: string }>`
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  color: white;
  flex-shrink: 0;
  background: ${props =>
    props.$imageUrl && props.$imageUrl.trim() !== ''
      ? `url(${props.$imageUrl})`
      : `linear-gradient(135deg, ${appTheme.colors.primary} 0%, #667eea 100%)`};
  background-size: cover;
  background-position: center;
  border: 2px solid white;
  box-shadow: ${appTheme.shadows.sm};

  /* Mobile adjustments */
  @media (max-width: ${appTheme.breakpoints.md}) {
    width: 36px;
    height: 36px;
    font-size: 13px;
  }
`;

const UserInfo = styled.div`
  flex: 1;
  min-width: 0;
`;

const UserName = styled.div`
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  /* Mobile adjustments */
  @media (max-width: ${appTheme.breakpoints.md}) {
    font-size: 15px;
  }
`;

const UserEmail = styled.div`
  font-size: 12px;
  opacity: 0.8;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  /* Mobile adjustments */
  @media (max-width: ${appTheme.breakpoints.md}) {
    font-size: 13px;
  }
`;

const UserRole = styled.div<{ $isOwner?: boolean; $isAdmin?: boolean }>`
  font-size: 10px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 4px;
  background: ${props => {
    if (props.$isOwner) return '#7c3aed';
    if (props.$isAdmin) return '#059669';
    return appTheme.colors.text.light;
  }};
  color: white;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-top: 2px;
  align-self: flex-start;

  /* Mobile adjustments */
  @media (max-width: ${appTheme.breakpoints.md}) {
    font-size: 11px;
    padding: 3px 8px;
  }
`;

const LoadingContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: ${appTheme.spacing.lg};
  color: ${appTheme.colors.text.secondary};
  gap: ${appTheme.spacing.sm};
  font-size: 14px;

  svg {
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  /* Mobile adjustments */
  @media (max-width: ${appTheme.breakpoints.md}) {
    padding: ${appTheme.spacing.md};
    font-size: 15px;
  }
`;

const EmptyState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: ${appTheme.spacing.lg};
  color: ${appTheme.colors.text.secondary};
  text-align: center;
  gap: ${appTheme.spacing.sm};

  /* Mobile adjustments */
  @media (max-width: ${appTheme.breakpoints.md}) {
    padding: ${appTheme.spacing.md};
  }
`;

const EmptyStateIcon = styled.div`
  color: ${appTheme.colors.text.light};
  margin-bottom: ${appTheme.spacing.xs};
`;

const EmptyStateText = styled.div`
  font-size: 14px;
  font-weight: 500;

  /* Mobile adjustments */
  @media (max-width: ${appTheme.breakpoints.md}) {
    font-size: 15px;
  }
`;

const EmptyStateSubtext = styled.div`
  font-size: 12px;
  opacity: 0.8;

  /* Mobile adjustments */
  @media (max-width: ${appTheme.breakpoints.md}) {
    font-size: 13px;
  }
`;

// Helper function to get user initials
const getUserInitials = (firstName: string, lastName: string): string => {
  return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
};

// Helper function to highlight search query in text
const highlightText = (text: string, query: string): React.ReactNode => {
  if (!query.trim()) return text;
  
  const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
  const parts = text.split(regex);
  
  return parts.map((part, index) => 
    regex.test(part) ? (
      <span key={index} style={{ fontWeight: 'bold', backgroundColor: 'rgba(255, 255, 255, 0.2)' }}>
        {part}
      </span>
    ) : part
  );
};

export default function MentionPopover({
  isOpen,
  position,
  suggestions,
  selectedIndex,
  loading,
  onSelect,
  onClose,
  searchQuery,
}: MentionPopoverProps) {
  const popoverRef = useRef<HTMLDivElement>(null);

  // Handle clicks outside the popover
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (popoverRef.current && !popoverRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isOpen, onClose]);

  // Scroll selected item into view
  useEffect(() => {
    if (isOpen && selectedIndex >= 0) {
      const selectedElement = popoverRef.current?.querySelector(`[data-index="${selectedIndex}"]`);
      selectedElement?.scrollIntoView({ block: 'nearest' });
    }
  }, [isOpen, selectedIndex]);

  if (!isOpen) return null;

  const handleUserSelect = (user: MentionUser) => {
    onSelect(user);
  };

  return (
    <PopoverContainer
      ref={popoverRef}
      $position={position}
      role="listbox"
      aria-label="User mention suggestions"
      aria-expanded="true"
    >
      <PopoverHeader id="mention-popover-header">
        {searchQuery ? `Searching for "${searchQuery}"` : 'Mention someone'}
      </PopoverHeader>
      
      <SuggestionsList
        role="list"
        aria-labelledby="mention-popover-header"
      >
        {loading ? (
          <LoadingContainer role="status" aria-live="polite">
            <Loader2 size={16} />
            Loading users...
          </LoadingContainer>
        ) : suggestions.length === 0 ? (
          <EmptyState role="status" aria-live="polite">
            <EmptyStateIcon aria-hidden="true">
              <User size={24} />
            </EmptyStateIcon>
            <EmptyStateText>No users found</EmptyStateText>
            <EmptyStateSubtext>
              {searchQuery ? 'Try a different search term' : 'No users available to mention'}
            </EmptyStateSubtext>
          </EmptyState>
        ) : (
          suggestions.map((user, index) => (
            <SuggestionItem
              key={user.id}
              data-index={index}
              $selected={index === selectedIndex}
              onClick={() => handleUserSelect(user)}
              role="option"
              aria-selected={index === selectedIndex}
              aria-label={`Mention ${user.firstName} ${user.lastName}, ${user.email}${user.userRole?.isOwner ? ', Owner' : user.userRole?.isAdmin ? ', Admin' : ''}`}
              tabIndex={-1}
            >
              <UserAvatar $imageUrl={user.imageUrl || undefined} aria-hidden="true">
                {!user.imageUrl && getUserInitials(user.firstName, user.lastName)}
              </UserAvatar>
              <UserInfo>
                <UserName>
                  {highlightText(`${user.firstName} ${user.lastName}`, searchQuery)}
                </UserName>
                <UserEmail>
                  {highlightText(user.email, searchQuery)}
                </UserEmail>
                {user.userRole && (user.userRole.isOwner || user.userRole.isAdmin) && (
                  <UserRole $isOwner={user.userRole.isOwner} $isAdmin={user.userRole.isAdmin}>
                    {user.userRole.isOwner ? 'Owner' : 'Admin'}
                  </UserRole>
                )}
              </UserInfo>
            </SuggestionItem>
          ))
        )}
      </SuggestionsList>
    </PopoverContainer>
  );
}
