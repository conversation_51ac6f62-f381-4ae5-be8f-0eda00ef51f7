'use client';

import React from 'react';
import styled from 'styled-components';
import { User } from 'lucide-react';
import { appTheme } from '@/app/theme';
import { Mention, MentionUser, MentionDisplayProps } from '@/types/mention';

// Mention tag for input fields (editable context)
const MentionTag = styled.span<{ $isInput?: boolean }>`
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 2px 8px;
  background: ${props => props.$isInput ? appTheme.colors.primary : 'rgba(99, 102, 241, 0.1)'};
  color: ${props => props.$isInput ? 'white' : appTheme.colors.primary};
  border-radius: 12px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid ${props => props.$isInput ? 'transparent' : appTheme.colors.primary};
  white-space: nowrap;
  user-select: none;
  position: relative;

  &:hover {
    background: ${props => props.$isInput ? appTheme.colors.primaryHover : 'rgba(99, 102, 241, 0.15)'};
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  &:active {
    transform: translateY(0);
  }

  /* Mobile adjustments */
  @media (max-width: ${appTheme.breakpoints.md}) {
    padding: 4px 10px;
    font-size: 14px;
    border-radius: 14px;
    gap: 6px;

    &:active {
      transform: scale(0.95);
    }
  }
`;

// Mention tag for sent messages (display context)
const MessageMention = styled.span<{ $isOwnMessage?: boolean }>`
  display: inline-flex;
  align-items: center;
  gap: 3px;
  padding: 1px 6px;
  background: ${props => props.$isOwnMessage ? 'rgba(255, 255, 255, 0.2)' : 'rgba(99, 102, 241, 0.15)'};
  color: ${props => props.$isOwnMessage ? 'rgba(255, 255, 255, 0.95)' : appTheme.colors.primary};
  border-radius: 8px;
  font-size: inherit;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid ${props => props.$isOwnMessage ? 'rgba(255, 255, 255, 0.3)' : 'rgba(99, 102, 241, 0.3)'};
  white-space: nowrap;
  text-decoration: none;

  &:hover {
    background: ${props => props.$isOwnMessage ? 'rgba(255, 255, 255, 0.3)' : 'rgba(99, 102, 241, 0.25)'};
    border-color: ${props => props.$isOwnMessage ? 'rgba(255, 255, 255, 0.5)' : 'rgba(99, 102, 241, 0.5)'};
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }

  /* Mobile adjustments */
  @media (max-width: ${appTheme.breakpoints.md}) {
    padding: 2px 8px;
    border-radius: 10px;
    gap: 4px;

    &:active {
      transform: scale(0.95);
    }
  }
`;

// Mini avatar for mentions
const MentionAvatar = styled.div<{ $imageUrl?: string; $size?: 'small' | 'tiny' }>`
  width: ${props => props.$size === 'tiny' ? '14px' : '16px'};
  height: ${props => props.$size === 'tiny' ? '14px' : '16px'};
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: ${props => props.$size === 'tiny' ? '6px' : '8px'};
  font-weight: 600;
  color: white;
  flex-shrink: 0;
  background: ${props =>
    props.$imageUrl && props.$imageUrl.trim() !== ''
      ? `url(${props.$imageUrl})`
      : `linear-gradient(135deg, ${appTheme.colors.primary} 0%, #667eea 100%)`};
  background-size: cover;
  background-position: center;
  border: 1px solid rgba(255, 255, 255, 0.5);

  /* Mobile adjustments */
  @media (max-width: ${appTheme.breakpoints.md}) {
    width: ${props => props.$size === 'tiny' ? '16px' : '18px'};
    height: ${props => props.$size === 'tiny' ? '16px' : '18px'};
    font-size: ${props => props.$size === 'tiny' ? '7px' : '9px'};
  }
`;

// Tooltip for mention hover
const MentionTooltip = styled.div`
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: ${appTheme.colors.text.primary};
  color: white;
  padding: 6px 10px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease;
  pointer-events: none;
  margin-bottom: 4px;

  &::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: ${appTheme.colors.text.primary};
  }

  ${MentionTag}:hover &,
  ${MessageMention}:hover & {
    opacity: 1;
    visibility: visible;
  }

  /* Mobile - hide tooltips on touch devices */
  @media (max-width: ${appTheme.breakpoints.md}) {
    display: none;
  }
`;

// Helper function to get user initials
const getUserInitials = (firstName: string, lastName: string): string => {
  return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
};

// Input mention component (for rich text editor)
interface InputMentionProps extends MentionDisplayProps {
  user?: MentionUser;
  onRemove?: () => void;
}

export function InputMention({ mention, user, onClick, onRemove, className, style }: InputMentionProps) {
  const displayName = user ? `${user.firstName} ${user.lastName}` : mention.displayName;
  const avatarUrl = user?.imageUrl;

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (onClick) {
      onClick(mention);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Backspace' || e.key === 'Delete') {
      e.preventDefault();
      e.stopPropagation();
      if (onRemove) {
        onRemove();
      }
    }
  };

  return (
    <MentionTag
      $isInput={true}
      className={className}
      style={style}
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      tabIndex={0}
      role="button"
      aria-label={`Mentioned user: ${displayName}`}
      contentEditable={false}
    >
      <MentionAvatar 
        $imageUrl={avatarUrl} 
        $size="small"
        aria-hidden="true"
      >
        {!avatarUrl && user && getUserInitials(user.firstName, user.lastName)}
      </MentionAvatar>
      {displayName}
      <MentionTooltip>
        {user?.email || displayName}
      </MentionTooltip>
    </MentionTag>
  );
}

// Message mention component (for sent messages)
interface MessageMentionProps extends MentionDisplayProps {
  user?: MentionUser;
  isOwnMessage?: boolean;
}

export function MessageMention({ 
  mention, 
  user, 
  onClick, 
  isOwnMessage = false, 
  className, 
  style 
}: MessageMentionProps) {
  const displayName = user ? `${user.firstName} ${user.lastName}` : mention.displayName;
  const avatarUrl = user?.imageUrl;

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (onClick) {
      onClick(mention);
    }
  };

  return (
    <MessageMention
      $isOwnMessage={isOwnMessage}
      className={className}
      style={style}
      onClick={handleClick}
      role="button"
      aria-label={`Mentioned user: ${displayName}`}
      tabIndex={0}
    >
      <MentionAvatar 
        $imageUrl={avatarUrl} 
        $size="tiny"
        aria-hidden="true"
      >
        {!avatarUrl && user && getUserInitials(user.firstName, user.lastName)}
      </MentionAvatar>
      @{displayName}
      <MentionTooltip>
        {user?.email || displayName}
      </MentionTooltip>
    </MessageMention>
  );
}

// Generic mention display component
export default function MentionDisplay({ 
  mention, 
  user, 
  onClick, 
  className, 
  style 
}: MentionDisplayProps) {
  return (
    <InputMention
      mention={mention}
      user={user}
      onClick={onClick}
      className={className}
      style={style}
    />
  );
}

// Utility function to render mentions in text content
export function renderMentionsInText(
  content: string, 
  mentions: Mention[], 
  users: MentionUser[], 
  isOwnMessage: boolean = false,
  onMentionClick?: (mention: Mention) => void
): React.ReactNode {
  if (!mentions.length) {
    return content;
  }

  // Sort mentions by start index to process them in order
  const sortedMentions = [...mentions].sort((a, b) => a.startIndex - b.startIndex);
  
  const parts: React.ReactNode[] = [];
  let lastIndex = 0;

  sortedMentions.forEach((mention, index) => {
    // Add text before mention
    if (mention.startIndex > lastIndex) {
      parts.push(content.slice(lastIndex, mention.startIndex));
    }

    // Find user data for this mention
    const user = users.find(u => u.id === mention.userId);

    // Add mention component
    parts.push(
      <MessageMention
        key={`mention-${mention.id}-${index}`}
        mention={mention}
        user={user}
        isOwnMessage={isOwnMessage}
        onClick={onMentionClick}
      />
    );

    lastIndex = mention.endIndex;
  });

  // Add remaining text after last mention
  if (lastIndex < content.length) {
    parts.push(content.slice(lastIndex));
  }

  return parts;
}
