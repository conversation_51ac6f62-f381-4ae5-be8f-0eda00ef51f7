/**
 * Utility functions for processing mentions in messages
 */

import { Mention, MentionUser, MentionParseResult } from '@/types/mention';

/**
 * Parse text content to extract mention data
 */
export function parseMentionsFromText(text: string, trigger: string = '@'): Mention[] {
  const mentions: Mention[] = [];
  const regex = new RegExp(`\\${trigger}([\\w\\s]+?)(?=\\s|$|[^\\w\\s])`, 'g');
  let match;

  while ((match = regex.exec(text)) !== null) {
    const displayName = match[1].trim();
    if (displayName) {
      mentions.push({
        id: `parsed-${Date.now()}-${mentions.length}`,
        userId: 0, // Will need to be resolved later
        displayName,
        startIndex: match.index,
        endIndex: match.index + match[0].length,
        trigger,
      });
    }
  }

  return mentions;
}

/**
 * Extract mentions from HTML content
 */
export function extractMentionsFromHTML(htmlContent: string): Mention[] {
  const mentions: Mention[] = [];
  const parser = new DOMParser();
  const doc = parser.parseFromString(htmlContent, 'text/html');
  
  // Find all mention elements
  const mentionElements = doc.querySelectorAll('[data-mention-id]');
  
  mentionElements.forEach((element, index) => {
    const mentionId = element.getAttribute('data-mention-id');
    const userId = element.getAttribute('data-user-id');
    const displayName = element.textContent?.replace('@', '') || '';
    
    if (mentionId && userId && displayName) {
      mentions.push({
        id: mentionId,
        userId: parseInt(userId, 10),
        displayName,
        startIndex: 0, // Will be calculated based on position in text
        endIndex: 0,
        trigger: '@',
      });
    }
  });

  return mentions;
}

/**
 * Convert mentions to a format suitable for API storage
 */
export function serializeMentions(mentions: Mention[]): string {
  return JSON.stringify(mentions.map(mention => ({
    id: mention.id,
    userId: mention.userId,
    displayName: mention.displayName,
    startIndex: mention.startIndex,
    endIndex: mention.endIndex,
    trigger: mention.trigger,
  })));
}

/**
 * Parse mentions from stored JSON string
 */
export function deserializeMentions(mentionsJson: string): Mention[] {
  try {
    const parsed = JSON.parse(mentionsJson);
    return Array.isArray(parsed) ? parsed : [];
  } catch (error) {
    console.error('Error parsing mentions JSON:', error);
    return [];
  }
}

/**
 * Replace mentions in text with formatted versions
 */
export function replaceMentionsInText(
  text: string,
  mentions: Mention[],
  users: MentionUser[],
  formatter: (mention: Mention, user?: MentionUser) => string
): string {
  let result = text;
  
  // Sort mentions by start index in descending order to avoid index shifting
  const sortedMentions = [...mentions].sort((a, b) => b.startIndex - a.startIndex);
  
  sortedMentions.forEach(mention => {
    const user = users.find(u => u.id === mention.userId);
    const replacement = formatter(mention, user);
    result = result.slice(0, mention.startIndex) + replacement + result.slice(mention.endIndex);
  });
  
  return result;
}

/**
 * Generate HTML content with mention elements
 */
export function generateMentionHTML(
  text: string,
  mentions: Mention[],
  users: MentionUser[]
): string {
  if (!mentions.length) return text;

  let result = text;
  
  // Sort mentions by start index in descending order
  const sortedMentions = [...mentions].sort((a, b) => b.startIndex - a.startIndex);
  
  sortedMentions.forEach(mention => {
    const user = users.find(u => u.id === mention.userId);
    const displayName = user ? `${user.firstName} ${user.lastName}` : mention.displayName;
    
    const mentionHTML = `<span 
      class="mention" 
      data-mention-id="${mention.id}" 
      data-user-id="${mention.userId}"
      contenteditable="false"
      style="
        display: inline-flex;
        align-items: center;
        gap: 4px;
        padding: 2px 8px;
        background: rgba(99, 102, 241, 0.1);
        color: rgb(99, 102, 241);
        border-radius: 12px;
        font-size: 13px;
        font-weight: 500;
        border: 1px solid rgb(99, 102, 241);
        white-space: nowrap;
        user-select: none;
      "
    >@${displayName}</span>`;
    
    result = result.slice(0, mention.startIndex) + mentionHTML + result.slice(mention.endIndex);
  });
  
  return result;
}

/**
 * Clean text content by removing mention formatting
 */
export function cleanMentionText(text: string): string {
  // Remove any HTML tags that might be left over
  return text.replace(/<[^>]*>/g, '').trim();
}

/**
 * Validate mention data
 */
export function validateMention(mention: Mention): boolean {
  return !!(
    mention.id &&
    mention.userId > 0 &&
    mention.displayName &&
    mention.startIndex >= 0 &&
    mention.endIndex > mention.startIndex &&
    mention.trigger
  );
}

/**
 * Update mention indices after text changes
 */
export function updateMentionIndices(
  mentions: Mention[],
  changeStart: number,
  changeLength: number,
  isInsertion: boolean
): Mention[] {
  return mentions.map(mention => {
    let newStartIndex = mention.startIndex;
    let newEndIndex = mention.endIndex;
    
    if (isInsertion) {
      // Text was inserted
      if (mention.startIndex >= changeStart) {
        newStartIndex += changeLength;
        newEndIndex += changeLength;
      } else if (mention.endIndex > changeStart) {
        newEndIndex += changeLength;
      }
    } else {
      // Text was deleted
      if (mention.startIndex >= changeStart + changeLength) {
        newStartIndex -= changeLength;
        newEndIndex -= changeLength;
      } else if (mention.startIndex >= changeStart) {
        // Mention was partially or fully deleted
        return null; // Mark for removal
      } else if (mention.endIndex > changeStart) {
        newEndIndex = Math.max(mention.startIndex, changeStart);
      }
    }
    
    return {
      ...mention,
      startIndex: newStartIndex,
      endIndex: newEndIndex,
    };
  }).filter((mention): mention is Mention => mention !== null);
}

/**
 * Find mentions that overlap with a text range
 */
export function findMentionsInRange(
  mentions: Mention[],
  start: number,
  end: number
): Mention[] {
  return mentions.filter(mention => 
    (mention.startIndex >= start && mention.startIndex < end) ||
    (mention.endIndex > start && mention.endIndex <= end) ||
    (mention.startIndex < start && mention.endIndex > end)
  );
}

/**
 * Get unique user IDs from mentions
 */
export function getUniqueUserIds(mentions: Mention[]): number[] {
  const userIds = mentions.map(mention => mention.userId);
  return [...new Set(userIds)].filter(id => id > 0);
}

/**
 * Create a mention object
 */
export function createMention(
  user: MentionUser,
  startIndex: number,
  endIndex: number,
  trigger: string = '@'
): Mention {
  return {
    id: `mention-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    userId: user.id,
    displayName: `${user.firstName} ${user.lastName}`,
    startIndex,
    endIndex,
    trigger,
  };
}

/**
 * Parse complete mention result from text and HTML
 */
export function parseMentionResult(
  text: string,
  htmlContent: string,
  mentions: Mention[]
): MentionParseResult {
  return {
    text: cleanMentionText(text),
    html: htmlContent,
    mentions: mentions.filter(validateMention),
  };
}

/**
 * Convert mention to notification data
 */
export function mentionToNotification(
  mention: Mention,
  user: MentionUser,
  messageId: string,
  senderId: number,
  chatId: number
) {
  return {
    type: 'mention',
    userId: mention.userId,
    senderId,
    messageId,
    chatId,
    mentionId: mention.id,
    displayName: mention.displayName,
    senderName: user ? `${user.firstName} ${user.lastName}` : 'Someone',
    timestamp: new Date().toISOString(),
  };
}

/**
 * Check if text contains potential mentions
 */
export function containsPotentialMentions(text: string, trigger: string = '@'): boolean {
  return text.includes(trigger);
}

/**
 * Escape mention text for safe HTML insertion
 */
export function escapeMentionText(text: string): string {
  return text
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;');
}
