/**
 * User mention types and interfaces for the chat system
 */

// Basic user information for mentions
export interface MentionUser {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  imageUrl?: string | null;
  userRole?: {
    id: number;
    name: string;
    isOwner: boolean;
    isAdmin: boolean;
    isMember: boolean;
  };
  isOnline?: boolean;
}

// Mention data structure that gets embedded in messages
export interface Mention {
  id: string; // Unique identifier for this mention instance
  userId: number; // ID of the mentioned user
  displayName: string; // Display name (firstName + lastName)
  startIndex: number; // Start position in the text content
  endIndex: number; // End position in the text content
  trigger: string; // The trigger character used (e.g., '@')
}

// Mention suggestion item for the popover
export interface MentionSuggestion extends MentionUser {
  matchScore?: number; // For search ranking
  highlighted?: string; // Highlighted text for search results
}

// Mention popover state
export interface MentionPopoverState {
  isOpen: boolean;
  position: { top: number; left: number };
  triggerIndex: number; // Position where '@' was typed
  searchQuery: string; // Text after '@' for filtering
  selectedIndex: number; // Currently selected suggestion index
  suggestions: MentionSuggestion[];
  loading: boolean;
}

// Mention input state for tracking mentions in the editor
export interface MentionInputState {
  mentions: Mention[];
  popover: MentionPopoverState;
  lastCursorPosition: number;
}

// Extended message interface to include mention data
export interface MessageWithMentions {
  id: string;
  senderId: string;
  senderName: string;
  content: string;
  htmlContent?: string; // Rich content with mention formatting
  timestamp: string;
  type: 'text' | 'image' | 'file' | 'sticker' | 'link';
  status: 'sending' | 'delivered' | 'read' | 'failed';
  mentions?: Mention[]; // Array of mentions in this message
  imageUrl?: string;
  user?: {
    id: number;
    firstName: string;
    lastName: string;
    imageUrl?: string;
  };
  readStatus?: any;
  isRead?: boolean;
  readAt?: string;
}

// Mention configuration options
export interface MentionConfig {
  trigger: string; // Default: '@'
  allowSpaceInQuery: boolean; // Allow spaces in search query
  maxSuggestions: number; // Maximum number of suggestions to show
  minQueryLength: number; // Minimum characters to start searching
  caseSensitive: boolean; // Case sensitive search
  highlightClassName: string; // CSS class for highlighted mentions
}

// Default mention configuration
export const DEFAULT_MENTION_CONFIG: MentionConfig = {
  trigger: '@',
  allowSpaceInQuery: false,
  maxSuggestions: 10,
  minQueryLength: 0,
  caseSensitive: false,
  highlightClassName: 'mention-highlight',
};

// Mention event types for callbacks
export type MentionEventType = 
  | 'mention-added'
  | 'mention-removed'
  | 'mention-clicked'
  | 'popover-opened'
  | 'popover-closed';

// Mention event data
export interface MentionEvent {
  type: MentionEventType;
  mention?: Mention;
  user?: MentionUser;
  position?: { start: number; end: number };
}

// Mention search options
export interface MentionSearchOptions {
  query: string;
  chatId?: number;
  organizationId?: number;
  departmentId?: number;
  excludeUserIds?: number[];
  limit?: number;
}

// Mention search result
export interface MentionSearchResult {
  users: MentionSuggestion[];
  hasMore: boolean;
  total: number;
}

// Utility type for mention parsing
export interface MentionParseResult {
  text: string;
  html: string;
  mentions: Mention[];
}

// Props for mention-related components
export interface MentionPopoverProps {
  isOpen: boolean;
  position: { top: number; left: number };
  suggestions: MentionSuggestion[];
  selectedIndex: number;
  loading: boolean;
  onSelect: (user: MentionUser) => void;
  onClose: () => void;
  searchQuery: string;
}

export interface MentionDisplayProps {
  mention: Mention;
  user?: MentionUser;
  onClick?: (mention: Mention) => void;
  className?: string;
  style?: React.CSSProperties;
}

export interface MentionInputProps {
  value: string;
  onChange: (value: string, mentions: Mention[]) => void;
  onMentionEvent?: (event: MentionEvent) => void;
  config?: Partial<MentionConfig>;
  users?: MentionUser[];
  onUserSearch?: (options: MentionSearchOptions) => Promise<MentionSearchResult>;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  style?: React.CSSProperties;
}

// Utility functions type definitions
export type MentionParser = {
  parseText: (text: string) => MentionParseResult;
  extractMentions: (text: string) => Mention[];
  replaceMentions: (text: string, mentions: Mention[], users: MentionUser[]) => string;
  generateMentionId: () => string;
};

// Hook return types
export interface UseMentionReturn {
  state: MentionInputState;
  actions: {
    openPopover: (position: { top: number; left: number }, triggerIndex: number) => void;
    closePopover: () => void;
    updateSearchQuery: (query: string) => void;
    selectSuggestion: (index: number) => void;
    addMention: (user: MentionUser, position: { start: number; end: number }) => void;
    removeMention: (mentionId: string) => void;
    updateMentions: (mentions: Mention[]) => void;
    searchUsers: (query: string) => Promise<void>;
  };
  utils: {
    getMentionAtPosition: (position: number) => Mention | null;
    getMentionById: (id: string) => Mention | null;
    formatMentionDisplay: (mention: Mention, user?: MentionUser) => string;
  };
}
