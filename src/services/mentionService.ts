/**
 * Service for handling user mentions in chat
 * Provides functionality to fetch users for mentions and manage mention data
 */

import { MentionUser, MentionSearchOptions, MentionSearchResult, MentionSuggestion } from '@/types/mention';
import { chatUserApi, dataApi } from './chatService';

// Cache for user data to avoid repeated API calls
const userCache = new Map<string, { data: MentionUser[]; timestamp: number }>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

/**
 * Fetch users for mentions based on search options
 */
export async function fetchMentionUsers(options: MentionSearchOptions): Promise<MentionSearchResult> {
  try {
    const { query, chatId, organizationId, departmentId, excludeUserIds = [], limit = 10 } = options;
    
    // Generate cache key
    const cacheKey = `${chatId || 'no-chat'}-${organizationId || 'no-org'}-${departmentId || 'no-dept'}`;
    
    // Check cache first
    const cached = userCache.get(cacheKey);
    const now = Date.now();
    
    let users: MentionUser[] = [];
    
    if (cached && (now - cached.timestamp) < CACHE_DURATION) {
      users = cached.data;
    } else {
      // Fetch fresh data
      users = await fetchUsersFromAPI(chatId, organizationId, departmentId);
      
      // Cache the results
      userCache.set(cacheKey, { data: users, timestamp: now });
    }
    
    // Filter out excluded users
    if (excludeUserIds.length > 0) {
      users = users.filter(user => !excludeUserIds.includes(user.id));
    }
    
    // Apply search filter
    let filteredUsers = users;
    if (query && query.trim()) {
      filteredUsers = searchUsers(users, query.trim());
    }
    
    // Apply limit
    const limitedUsers = filteredUsers.slice(0, limit);
    
    return {
      users: limitedUsers,
      hasMore: filteredUsers.length > limit,
      total: filteredUsers.length,
    };
  } catch (error) {
    console.error('Error fetching mention users:', error);
    return {
      users: [],
      hasMore: false,
      total: 0,
    };
  }
}

/**
 * Fetch users from appropriate API based on context
 */
async function fetchUsersFromAPI(
  chatId?: number,
  organizationId?: number,
  departmentId?: number
): Promise<MentionUser[]> {
  try {
    // Priority 1: If chatId is provided, get chat users
    if (chatId) {
      const response = await chatUserApi.getChatUsers(chatId);
      if (response.success && response.data) {
        return response.data.map(transformChatUserToMentionUser);
      }
    }
    
    // Priority 2: If departmentId is provided, get department members
    if (departmentId) {
      const response = await dataApi.getDepartmentMembers(departmentId);
      if (response.success && response.data?.users) {
        return response.data.users.map(transformUserToMentionUser);
      }
    }
    
    // Priority 3: If organizationId is provided, get organization members
    if (organizationId) {
      const response = await dataApi.getOrganizationMembers(organizationId);
      if (response.success && response.data?.users) {
        return response.data.users.map(transformUserToMentionUser);
      }
    }
    
    // Fallback: Return empty array
    return [];
  } catch (error) {
    console.error('Error fetching users from API:', error);
    return [];
  }
}

/**
 * Transform chat user data to mention user format
 */
function transformChatUserToMentionUser(chatUser: any): MentionUser {
  const user = chatUser.user || chatUser;
  return {
    id: user.id,
    firstName: user.firstName,
    lastName: user.lastName,
    email: user.email,
    imageUrl: user.imageUrl,
    userRole: user.userRole ? {
      id: user.userRole.id,
      name: user.userRole.name,
      isOwner: user.userRole.isOwner || false,
      isAdmin: user.userRole.isAdmin || false,
      isMember: user.userRole.isMember || false,
    } : undefined,
    isOnline: user.isOnline || false,
  };
}

/**
 * Transform general user data to mention user format
 */
function transformUserToMentionUser(user: any): MentionUser {
  return {
    id: user.id,
    firstName: user.firstName,
    lastName: user.lastName,
    email: user.email,
    imageUrl: user.imageUrl,
    userRole: user.userRole ? {
      id: user.userRole.id,
      name: user.userRole.name,
      isOwner: user.userRole.isOwner || false,
      isAdmin: user.userRole.isAdmin || false,
      isMember: user.userRole.isMember || false,
    } : undefined,
    isOnline: false, // Default to offline for non-chat contexts
  };
}

/**
 * Search users based on query string
 */
function searchUsers(users: MentionUser[], query: string): MentionSuggestion[] {
  const lowerQuery = query.toLowerCase();
  
  return users
    .map(user => {
      const fullName = `${user.firstName} ${user.lastName}`.toLowerCase();
      const email = user.email.toLowerCase();
      
      let matchScore = 0;
      let highlighted = '';
      
      // Exact name match (highest priority)
      if (fullName === lowerQuery) {
        matchScore = 100;
        highlighted = `${user.firstName} ${user.lastName}`;
      }
      // Name starts with query
      else if (fullName.startsWith(lowerQuery)) {
        matchScore = 90;
        highlighted = `${user.firstName} ${user.lastName}`;
      }
      // First name starts with query
      else if (user.firstName.toLowerCase().startsWith(lowerQuery)) {
        matchScore = 80;
        highlighted = user.firstName;
      }
      // Last name starts with query
      else if (user.lastName.toLowerCase().startsWith(lowerQuery)) {
        matchScore = 75;
        highlighted = user.lastName;
      }
      // Email starts with query
      else if (email.startsWith(lowerQuery)) {
        matchScore = 70;
        highlighted = user.email;
      }
      // Name contains query
      else if (fullName.includes(lowerQuery)) {
        matchScore = 60;
        highlighted = `${user.firstName} ${user.lastName}`;
      }
      // First name contains query
      else if (user.firstName.toLowerCase().includes(lowerQuery)) {
        matchScore = 50;
        highlighted = user.firstName;
      }
      // Last name contains query
      else if (user.lastName.toLowerCase().includes(lowerQuery)) {
        matchScore = 45;
        highlighted = user.lastName;
      }
      // Email contains query
      else if (email.includes(lowerQuery)) {
        matchScore = 40;
        highlighted = user.email;
      }
      
      return matchScore > 0 ? {
        ...user,
        matchScore,
        highlighted,
      } : null;
    })
    .filter((user): user is MentionSuggestion => user !== null)
    .sort((a, b) => {
      // Sort by match score (descending), then by name (ascending)
      if (b.matchScore !== a.matchScore) {
        return b.matchScore - a.matchScore;
      }
      const nameA = `${a.firstName} ${a.lastName}`;
      const nameB = `${b.firstName} ${b.lastName}`;
      return nameA.localeCompare(nameB);
    });
}

/**
 * Get user by ID (with caching)
 */
export async function getMentionUserById(userId: number): Promise<MentionUser | null> {
  try {
    // Check all cached data first
    for (const cached of userCache.values()) {
      const user = cached.data.find(u => u.id === userId);
      if (user) {
        return user;
      }
    }
    
    // If not found in cache, make API call
    // Note: This would require a specific user API endpoint
    // For now, return null and rely on cached data
    return null;
  } catch (error) {
    console.error('Error getting mention user by ID:', error);
    return null;
  }
}

/**
 * Clear user cache (useful for refreshing data)
 */
export function clearMentionUserCache(): void {
  userCache.clear();
}

/**
 * Get cache statistics (for debugging)
 */
export function getMentionCacheStats(): { size: number; keys: string[] } {
  return {
    size: userCache.size,
    keys: Array.from(userCache.keys()),
  };
}

/**
 * Preload users for a specific context (useful for performance)
 */
export async function preloadMentionUsers(
  chatId?: number,
  organizationId?: number,
  departmentId?: number
): Promise<void> {
  try {
    await fetchMentionUsers({
      query: '',
      chatId,
      organizationId,
      departmentId,
      limit: 100, // Load more users for preloading
    });
  } catch (error) {
    console.error('Error preloading mention users:', error);
  }
}

/**
 * Debounced search function for real-time search
 */
let searchTimeout: NodeJS.Timeout | null = null;

export function debouncedMentionSearch(
  options: MentionSearchOptions,
  callback: (result: MentionSearchResult) => void,
  delay: number = 300
): void {
  if (searchTimeout) {
    clearTimeout(searchTimeout);
  }
  
  searchTimeout = setTimeout(async () => {
    const result = await fetchMentionUsers(options);
    callback(result);
  }, delay);
}

/**
 * Cancel any pending debounced search
 */
export function cancelDebouncedSearch(): void {
  if (searchTimeout) {
    clearTimeout(searchTimeout);
    searchTimeout = null;
  }
}
