'use client';

import React, { useState } from 'react';
import styled from 'styled-components';
import { appTheme } from '@/app/theme';
import RichTextEditorWithMentions from '@/app/(main)/chat/components/RichTextEditorWithMentions';
import { renderMentionsInText } from '@/app/(main)/chat/components/MentionDisplay';
import { Mention, MentionUser } from '@/types/mention';

const TestContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
  padding: ${appTheme.spacing.xl};
  font-family: ${appTheme.typography.fontFamily};
`;

const TestSection = styled.div`
  margin-bottom: ${appTheme.spacing.xl};
  padding: ${appTheme.spacing.lg};
  border: 1px solid ${appTheme.colors.border};
  border-radius: ${appTheme.borderRadius.lg};
  background: ${appTheme.colors.background.main};
`;

const TestTitle = styled.h2`
  color: ${appTheme.colors.text.primary};
  margin-bottom: ${appTheme.spacing.md};
  font-size: 18px;
  font-weight: 600;
`;

const TestDescription = styled.p`
  color: ${appTheme.colors.text.secondary};
  margin-bottom: ${appTheme.spacing.lg};
  font-size: 14px;
  line-height: 1.5;
`;

const MessagePreview = styled.div<{ $isOwn?: boolean }>`
  padding: ${appTheme.spacing.md} ${appTheme.spacing.lg};
  border-radius: ${appTheme.borderRadius.lg};
  background: ${props => props.$isOwn ? appTheme.colors.primary : appTheme.colors.background.lighter};
  color: ${props => props.$isOwn ? 'white' : appTheme.colors.text.primary};
  margin: ${appTheme.spacing.md} 0;
  font-size: 15px;
  line-height: 1.5;
`;

const DebugInfo = styled.pre`
  background: #f5f5f5;
  padding: ${appTheme.spacing.md};
  border-radius: ${appTheme.borderRadius.md};
  font-size: 12px;
  overflow-x: auto;
  margin-top: ${appTheme.spacing.md};
`;

const Button = styled.button`
  padding: ${appTheme.spacing.sm} ${appTheme.spacing.md};
  background: ${appTheme.colors.primary};
  color: white;
  border: none;
  border-radius: ${appTheme.borderRadius.md};
  cursor: pointer;
  font-size: 14px;
  margin-right: ${appTheme.spacing.sm};
  
  &:hover {
    background: ${appTheme.colors.primaryHover};
  }
`;

// Mock users for testing
const mockUsers: MentionUser[] = [
  {
    id: 1,
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    imageUrl: null,
    userRole: {
      id: 1,
      name: 'Admin',
      isOwner: false,
      isAdmin: true,
      isMember: false,
    },
    isOnline: true,
  },
  {
    id: 2,
    firstName: 'Jane',
    lastName: 'Smith',
    email: '<EMAIL>',
    imageUrl: null,
    userRole: {
      id: 2,
      name: 'Member',
      isOwner: false,
      isAdmin: false,
      isMember: true,
    },
    isOnline: false,
  },
  {
    id: 3,
    firstName: 'Bob',
    lastName: 'Johnson',
    email: '<EMAIL>',
    imageUrl: null,
    userRole: {
      id: 3,
      name: 'Owner',
      isOwner: true,
      isAdmin: false,
      isMember: false,
    },
    isOnline: true,
  },
];

export default function MentionFeatureTest() {
  const [messageContent, setMessageContent] = useState('');
  const [messageHtml, setMessageHtml] = useState('');
  const [mentions, setMentions] = useState<Mention[]>([]);
  const [sentMessages, setSentMessages] = useState<Array<{
    content: string;
    html: string;
    mentions: Mention[];
    timestamp: string;
  }>>([]);

  const handleSendMessage = () => {
    if (messageContent.trim()) {
      setSentMessages(prev => [...prev, {
        content: messageContent,
        html: messageHtml,
        mentions: mentions,
        timestamp: new Date().toLocaleTimeString(),
      }]);
      
      setMessageContent('');
      setMessageHtml('');
      setMentions([]);
    }
  };

  const handleClearMessages = () => {
    setSentMessages([]);
  };

  const mockSearchUsers = async (options: any) => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));
    
    const { query = '', limit = 10 } = options;
    
    let filteredUsers = mockUsers;
    
    if (query) {
      const lowerQuery = query.toLowerCase();
      filteredUsers = mockUsers.filter(user => 
        user.firstName.toLowerCase().includes(lowerQuery) ||
        user.lastName.toLowerCase().includes(lowerQuery) ||
        user.email.toLowerCase().includes(lowerQuery)
      );
    }
    
    return {
      users: filteredUsers.slice(0, limit),
      hasMore: filteredUsers.length > limit,
      total: filteredUsers.length,
    };
  };

  return (
    <TestContainer>
      <h1 style={{ textAlign: 'center', marginBottom: '2rem', color: appTheme.colors.text.primary }}>
        User Mention Feature Test
      </h1>
      
      <TestSection>
        <TestTitle>1. Message Input with Mentions</TestTitle>
        <TestDescription>
          Type "@" to trigger the mention popover. Try searching for "John", "Jane", or "Bob".
          Use arrow keys to navigate and Enter to select.
        </TestDescription>
        
        <RichTextEditorWithMentions
          placeholder="Type a message with mentions..."
          value={messageHtml}
          onChange={(html, text, mentionList) => {
            setMessageHtml(html);
            setMessageContent(text);
            setMentions(mentionList);
          }}
          mentionSearchOptions={{
            limit: 5,
          }}
          onMentionClick={(mention, user) => {
            console.log('Mention clicked:', mention, user);
          }}
        />
        
        <div style={{ marginTop: '1rem' }}>
          <Button onClick={handleSendMessage} disabled={!messageContent.trim()}>
            Send Message
          </Button>
          <Button onClick={handleClearMessages}>
            Clear All Messages
          </Button>
        </div>
        
        <DebugInfo>
          Content: {messageContent}
          {'\n'}Mentions: {JSON.stringify(mentions, null, 2)}
        </DebugInfo>
      </TestSection>

      <TestSection>
        <TestTitle>2. Message Display with Mentions</TestTitle>
        <TestDescription>
          Messages you send will appear here with properly rendered mentions.
          Click on mentions to test interaction.
        </TestDescription>
        
        {sentMessages.length === 0 ? (
          <p style={{ color: appTheme.colors.text.secondary, fontStyle: 'italic' }}>
            No messages sent yet. Try sending a message with mentions above.
          </p>
        ) : (
          sentMessages.map((message, index) => (
            <div key={index}>
              <MessagePreview $isOwn={index % 2 === 0}>
                {message.mentions.length > 0 ? (
                  renderMentionsInText(
                    message.content,
                    message.mentions,
                    mockUsers,
                    index % 2 === 0,
                    (mention) => console.log('Message mention clicked:', mention)
                  )
                ) : (
                  message.content
                )}
              </MessagePreview>
              <small style={{ color: appTheme.colors.text.light }}>
                Sent at {message.timestamp}
              </small>
            </div>
          ))
        )}
      </TestSection>

      <TestSection>
        <TestTitle>3. Feature Testing Checklist</TestTitle>
        <TestDescription>
          Test the following functionality:
        </TestDescription>
        
        <ul style={{ color: appTheme.colors.text.secondary, lineHeight: 1.6 }}>
          <li>✅ Type "@" to open mention popover</li>
          <li>✅ Search users by typing after "@"</li>
          <li>✅ Navigate suggestions with arrow keys</li>
          <li>✅ Select user with Enter key</li>
          <li>✅ Close popover with Escape key</li>
          <li>✅ Click to select user from popover</li>
          <li>✅ Mentions display in input field</li>
          <li>✅ Mentions display in sent messages</li>
          <li>✅ Click mentions in messages</li>
          <li>✅ Responsive design on mobile</li>
          <li>✅ Accessibility with screen readers</li>
          <li>✅ Multiple mentions in one message</li>
        </ul>
      </TestSection>

      <TestSection>
        <TestTitle>4. Mock Data</TestTitle>
        <TestDescription>
          Available users for testing:
        </TestDescription>
        
        <DebugInfo>
          {JSON.stringify(mockUsers, null, 2)}
        </DebugInfo>
      </TestSection>
    </TestContainer>
  );
}
