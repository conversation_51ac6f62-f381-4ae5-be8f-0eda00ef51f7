# User Mention Feature Documentation

## Overview

The user mention feature allows users to mention other users in chat messages by typing "@" followed by the user's name. This feature includes:

- **Trigger mechanism**: "@" character triggers a user selection popover
- **User selection**: Searchable dropdown with available users
- **Visual distinction**: Mentioned users are highlighted in both input and sent messages
- **Accessibility**: Full keyboard navigation and screen reader support
- **Responsive design**: Touch-friendly interface for mobile devices

## Architecture

### Core Components

1. **Types and Interfaces** (`src/types/mention.ts`)
   - `MentionUser`: User data structure for mentions
   - `Mention`: Individual mention data
   - `MentionPopoverState`: Popover state management
   - `MentionConfig`: Configuration options

2. **UI Components**
   - `MentionPopover`: Dropdown for user selection
   - `MentionDisplay`: Visual representation of mentions
   - `RichTextEditorWithMentions`: Enhanced text editor with mention support

3. **Services and Utilities**
   - `mentionService.ts`: User fetching and caching
   - `mentionUtils.ts`: Text processing and parsing
   - `useMention.ts`: React hook for mention functionality

### Data Flow

1. User types "@" in the message input
2. Cursor position is tracked and popover is triggered
3. Users are fetched based on chat/organization context
4. User selection updates the input with mention tags
5. Message is sent with mention metadata
6. Received messages render mentions with proper styling

## Implementation Details

### Trigger Detection

```typescript
// Detects "@" character and opens popover
const checkForMentionTrigger = () => {
  const textContent = editorRef.current.textContent || '';
  const cursorPos = getCursorPosition(editorRef.current);
  const beforeCursor = textContent.slice(0, cursorPos);
  const atIndex = beforeCursor.lastIndexOf('@');
  
  if (atIndex !== -1 && isValidTrigger(atIndex, beforeCursor)) {
    openMentionPopover(atIndex);
  }
};
```

### User Search

```typescript
// Searches users with caching and filtering
export async function fetchMentionUsers(options: MentionSearchOptions) {
  const { query, chatId, organizationId, excludeUserIds } = options;
  
  // Check cache first
  const cached = userCache.get(cacheKey);
  if (cached && !isExpired(cached)) {
    return filterAndSort(cached.data, query);
  }
  
  // Fetch from API
  const users = await fetchUsersFromAPI(chatId, organizationId);
  userCache.set(cacheKey, { data: users, timestamp: Date.now() });
  
  return filterAndSort(users, query);
}
```

### Mention Rendering

```typescript
// Renders mentions in message content
export function renderMentionsInText(
  content: string,
  mentions: Mention[],
  users: MentionUser[],
  isOwnMessage: boolean
) {
  const sortedMentions = mentions.sort((a, b) => a.startIndex - b.startIndex);
  const parts = [];
  let lastIndex = 0;

  sortedMentions.forEach(mention => {
    // Add text before mention
    parts.push(content.slice(lastIndex, mention.startIndex));
    
    // Add mention component
    parts.push(
      <MessageMention
        key={mention.id}
        mention={mention}
        user={users.find(u => u.id === mention.userId)}
        isOwnMessage={isOwnMessage}
      />
    );
    
    lastIndex = mention.endIndex;
  });

  // Add remaining text
  parts.push(content.slice(lastIndex));
  return parts;
}
```

## Usage

### Basic Integration

```tsx
import RichTextEditorWithMentions from './RichTextEditorWithMentions';

function ChatInput({ onSendMessage, chatId }) {
  const [content, setContent] = useState('');
  const [mentions, setMentions] = useState([]);

  return (
    <RichTextEditorWithMentions
      value={content}
      onChange={(html, text, mentions) => {
        setContent(text);
        setMentions(mentions);
      }}
      mentionSearchOptions={{
        chatId,
        excludeUserIds: [currentUser.id],
      }}
      onMentionClick={(mention, user) => {
        console.log('Mention clicked:', mention, user);
      }}
    />
  );
}
```

### Message Rendering

```tsx
import { renderMentionsInText } from './MentionDisplay';

function MessageBubble({ message, users, isOwnMessage }) {
  const mentions = message.mentions || [];
  
  return (
    <div className="message-bubble">
      {mentions.length > 0 ? (
        renderMentionsInText(
          message.content,
          mentions,
          users,
          isOwnMessage
        )
      ) : (
        message.content
      )}
    </div>
  );
}
```

## Configuration

### Mention Config

```typescript
const mentionConfig: MentionConfig = {
  trigger: '@',                    // Trigger character
  allowSpaceInQuery: false,        // Allow spaces in search
  maxSuggestions: 10,             // Max suggestions shown
  minQueryLength: 0,              // Min chars to start search
  caseSensitive: false,           // Case sensitive search
  highlightClassName: 'mention',   // CSS class for highlights
};
```

### Search Options

```typescript
const searchOptions: MentionSearchOptions = {
  chatId: 123,                    // Current chat ID
  organizationId: 456,            // Organization context
  departmentId: 789,              // Department context
  excludeUserIds: [currentUserId], // Users to exclude
  limit: 10,                      // Result limit
};
```

## Accessibility Features

### Keyboard Navigation
- **Arrow Keys**: Navigate through suggestions
- **Enter**: Select highlighted suggestion
- **Escape**: Close popover
- **Tab**: Move focus away from mention input

### Screen Reader Support
- ARIA labels for all interactive elements
- Live regions for dynamic content updates
- Proper role attributes (listbox, option, etc.)
- Descriptive text for mention elements

### Focus Management
- Proper focus trapping in popover
- Focus restoration after selection
- Visual focus indicators

## Mobile Responsiveness

### Touch Interactions
- Touch-friendly button sizes (44px minimum)
- Proper touch event handling
- Swipe gestures for navigation

### Layout Adaptations
- Responsive popover positioning
- Adaptive font sizes
- Optimized spacing for mobile

### Performance
- Debounced search queries
- Efficient re-rendering
- Memory management for images

## API Integration

### Message Storage
Messages with mentions should store mention metadata:

```json
{
  "id": "msg-123",
  "content": "Hey @John Doe, can you review this?",
  "mentions": [
    {
      "id": "mention-1",
      "userId": 456,
      "displayName": "John Doe",
      "startIndex": 4,
      "endIndex": 13,
      "trigger": "@"
    }
  ]
}
```

### User Endpoints
Required API endpoints:
- `GET /api/chat-user?chatId={id}` - Get chat participants
- `GET /api/organization-members?orgId={id}` - Get org members
- `GET /api/department-members?deptId={id}` - Get dept members

## Testing

### Unit Tests
- Mention parsing and validation
- User search and filtering
- Component rendering with mentions

### Integration Tests
- End-to-end mention workflow
- API integration
- Cross-browser compatibility

### Accessibility Tests
- Screen reader compatibility
- Keyboard navigation
- Color contrast compliance

## Performance Considerations

### Caching Strategy
- User data cached for 5 minutes
- Debounced search queries (300ms)
- Efficient DOM updates

### Memory Management
- Cleanup of event listeners
- Proper component unmounting
- Image URL revocation

### Bundle Size
- Tree-shaking friendly exports
- Lazy loading of components
- Optimized dependencies

## Browser Support

- **Modern Browsers**: Full feature support
- **IE11**: Basic functionality with polyfills
- **Mobile Safari**: Touch-optimized experience
- **Chrome/Firefox**: Full feature parity

## Future Enhancements

1. **Group Mentions**: @everyone, @channel, @here
2. **Mention Notifications**: Push notifications for mentions
3. **Mention Analytics**: Track mention engagement
4. **Custom Triggers**: Support for multiple trigger characters
5. **Rich Mentions**: Include user avatars in mentions
6. **Mention Permissions**: Control who can mention whom

## Troubleshooting

### Common Issues

1. **Popover not appearing**
   - Check cursor position detection
   - Verify user data is loaded
   - Ensure proper event handling

2. **Search not working**
   - Verify API endpoints
   - Check network connectivity
   - Review search query formatting

3. **Mentions not rendering**
   - Validate mention data structure
   - Check user data availability
   - Verify component integration

### Debug Tools

```typescript
// Enable debug logging
localStorage.setItem('mention-debug', 'true');

// Check cache status
import { getMentionCacheStats } from './mentionService';
console.log(getMentionCacheStats());

// Validate mention data
import { validateMention } from './mentionUtils';
mentions.forEach(mention => {
  if (!validateMention(mention)) {
    console.error('Invalid mention:', mention);
  }
});
```
